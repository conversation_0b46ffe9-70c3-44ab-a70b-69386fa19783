"""
Tests for the enhanced LLM service conversation functionality.
"""

import pytest
from unittest.mock import Mock, patch
from core.llm_service import LLMService


class TestLLMConversationMethods:
    """Test the new conversation methods in LLMService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        with patch('core.llm_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = "test-key"
            mock_settings.OPENAI_MODEL = "gpt-4"
            mock_settings.MAX_TOKENS = 1000
            mock_settings.TEMPERATURE = 0.7
            self.llm_service = LLMService()
    
    def test_create_message(self):
        """Test message creation helper."""
        message = self.llm_service.create_message("user", "Hello world")
        assert message == {"role": "user", "content": "Hello world"}
        
        # Test invalid role
        with pytest.raises(ValueError, match="Role must be"):
            self.llm_service.create_message("invalid", "content")
    
    def test_build_conversation_messages(self):
        """Test conversation message builder."""
        # Test with all components
        history = [
            {"role": "user", "content": "Previous message"},
            {"role": "assistant", "content": "Previous response"}
        ]
        
        messages = self.llm_service.build_conversation_messages(
            system_prompt="You are helpful",
            conversation_history=history,
            user_message="New message"
        )
        
        expected = [
            {"role": "system", "content": "You are helpful"},
            {"role": "user", "content": "Previous message"},
            {"role": "assistant", "content": "Previous response"},
            {"role": "user", "content": "New message"}
        ]
        
        assert messages == expected
        
        # Test with only system prompt
        messages = self.llm_service.build_conversation_messages(
            system_prompt="You are helpful"
        )
        assert messages == [{"role": "system", "content": "You are helpful"}]
        
        # Test with invalid history format
        with pytest.raises(ValueError, match="Each history message must be a dict"):
            self.llm_service.build_conversation_messages(
                conversation_history=["invalid"]
            )
    
    def test_truncate_conversation_history(self):
        """Test conversation history truncation."""
        # Create test messages
        messages = [
            {"role": "system", "content": "System message"},
            {"role": "user", "content": "User 1"},
            {"role": "assistant", "content": "Assistant 1"},
            {"role": "user", "content": "User 2"},
            {"role": "assistant", "content": "Assistant 2"},
            {"role": "user", "content": "User 3"},
            {"role": "assistant", "content": "Assistant 3"}
        ]
        
        # Test truncation with system preservation
        truncated = self.llm_service.truncate_conversation_history(
            messages, max_messages=4, preserve_system=True
        )
        
        # Should keep system message + 3 most recent others
        assert len(truncated) == 4
        assert truncated[0]["role"] == "system"
        assert truncated[-1]["content"] == "Assistant 3"
        
        # Test without system preservation
        truncated = self.llm_service.truncate_conversation_history(
            messages, max_messages=3, preserve_system=False
        )
        
        # Should keep 3 most recent messages
        assert len(truncated) == 3
        assert truncated[-1]["content"] == "Assistant 3"
        
        # Test when no truncation needed
        truncated = self.llm_service.truncate_conversation_history(
            messages, max_messages=10
        )
        assert len(truncated) == len(messages)
    
    @patch('core.llm_service.OpenAI')
    def test_generate_conversation_completion(self, mock_openai):
        """Test conversation completion generation."""
        # Mock the OpenAI response
        mock_response = Mock()
        mock_response.choices[0].message.content = "Test response"
        mock_openai.return_value.chat.completions.create.return_value = mock_response
        
        messages = [
            {"role": "system", "content": "You are helpful"},
            {"role": "user", "content": "Hello"}
        ]
        
        response = self.llm_service.generate_conversation_completion(messages)
        assert response == "Test response"
        
        # Verify the API was called correctly
        mock_openai.return_value.chat.completions.create.assert_called_once()
        call_args = mock_openai.return_value.chat.completions.create.call_args[1]
        assert call_args["messages"] == messages
        assert call_args["model"] == "gpt-4"
    
    def test_conversation_completion_validation(self):
        """Test message validation in conversation completion."""
        # Test invalid message format
        invalid_messages = [
            {"role": "user"}  # Missing content
        ]
        
        with pytest.raises(ValueError, match="Each message must be a dict"):
            self.llm_service.generate_conversation_completion(invalid_messages)
        
        # Test invalid role
        invalid_messages = [
            {"role": "invalid", "content": "test"}
        ]
        
        with pytest.raises(ValueError, match="Message role must be"):
            self.llm_service.generate_conversation_completion(invalid_messages)
    
    @patch('core.llm_service.OpenAI')
    def test_generate_conversation_structured_response(self, mock_openai):
        """Test structured conversation response generation."""
        # Mock the OpenAI response
        mock_response = Mock()
        mock_response.choices[0].message.content = '{"result": "test"}'
        mock_openai.return_value.chat.completions.create.return_value = mock_response
        
        messages = [
            {"role": "system", "content": "You are helpful"},
            {"role": "user", "content": "Hello"}
        ]
        
        schema = {
            "type": "object",
            "properties": {"result": {"type": "string"}},
            "required": ["result"]
        }
        
        response = self.llm_service.generate_conversation_structured_response(
            messages, schema
        )
        assert response == {"result": "test"}
        
        # Verify the API was called with response_format
        call_args = mock_openai.return_value.chat.completions.create.call_args[1]
        assert "response_format" in call_args
        assert call_args["response_format"]["type"] == "json_schema"


if __name__ == "__main__":
    pytest.main([__file__])

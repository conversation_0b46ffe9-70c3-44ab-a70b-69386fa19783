"""
Example demonstrating how to use the enhanced LLM service for multi-message conversations.
This shows how to build and manage conversation history for discussions.
"""

from core.llm_service import llm_service

def example_basic_conversation():
    """Example of a basic multi-message conversation."""
    print("=== Basic Conversation Example ===")
    
    # Create conversation messages manually
    messages = [
        llm_service.create_message("system", "You are a helpful assistant that provides thoughtful responses."),
        llm_service.create_message("user", "Hello! Can you tell me about the benefits of exercise?"),
        llm_service.create_message("assistant", "Hello! Exercise has many benefits including improved cardiovascular health, stronger muscles and bones, better mental health, and increased energy levels."),
        llm_service.create_message("user", "That's great! Can you elaborate on the mental health benefits?")
    ]
    
    # Generate response using conversation completion
    response = llm_service.generate_conversation_completion(messages)
    print(f"Assistant: {response}")
    
    return messages, response

def example_conversation_builder():
    """Example using the conversation builder helper method."""
    print("\n=== Conversation Builder Example ===")
    
    # Build conversation using helper method
    system_prompt = "You are a knowledgeable fitness coach who provides personalized advice."
    
    # Start with some conversation history
    conversation_history = [
        llm_service.create_message("user", "I want to start working out but I'm a beginner."),
        llm_service.create_message("assistant", "That's wonderful! Starting a fitness journey is a great decision. For beginners, I recommend starting with 3 days per week of light exercise."),
        llm_service.create_message("user", "What kind of exercises should I focus on?"),
        llm_service.create_message("assistant", "For beginners, I suggest focusing on bodyweight exercises like squats, push-ups, and planks, along with some light cardio like walking or cycling.")
    ]
    
    # Add new user message
    new_user_message = "How long should each workout session be?"
    
    # Build complete message list
    messages = llm_service.build_conversation_messages(
        system_prompt=system_prompt,
        conversation_history=conversation_history,
        user_message=new_user_message
    )
    
    # Generate response
    response = llm_service.generate_conversation_completion(messages)
    print(f"User: {new_user_message}")
    print(f"Assistant: {response}")
    
    return messages, response

def example_structured_conversation():
    """Example of structured response in a conversation context."""
    print("\n=== Structured Conversation Example ===")
    
    # Conversation about meal planning
    messages = [
        llm_service.create_message("system", "You are a nutritionist who provides structured meal planning advice."),
        llm_service.create_message("user", "I'm trying to eat healthier. Can you suggest a meal plan?"),
        llm_service.create_message("assistant", "I'd be happy to help! To create a personalized meal plan, I need to know about your dietary preferences, goals, and any restrictions."),
        llm_service.create_message("user", "I want to lose weight, I'm vegetarian, and I need 3 meals and 2 snacks per day.")
    ]
    
    # Schema for structured meal plan response
    schema = {
        "type": "object",
        "properties": {
            "meal_plan": {
                "type": "object",
                "properties": {
                    "breakfast": {"type": "string"},
                    "lunch": {"type": "string"},
                    "dinner": {"type": "string"},
                    "snack1": {"type": "string"},
                    "snack2": {"type": "string"}
                },
                "required": ["breakfast", "lunch", "dinner", "snack1", "snack2"]
            },
            "total_calories": {"type": "integer"},
            "notes": {"type": "string"}
        },
        "required": ["meal_plan", "total_calories", "notes"]
    }
    
    # Generate structured response
    response = llm_service.generate_conversation_structured_response(
        messages=messages,
        schema=schema
    )
    
    print("Structured Meal Plan Response:")
    print(f"Breakfast: {response['meal_plan']['breakfast']}")
    print(f"Lunch: {response['meal_plan']['lunch']}")
    print(f"Dinner: {response['meal_plan']['dinner']}")
    print(f"Snack 1: {response['meal_plan']['snack1']}")
    print(f"Snack 2: {response['meal_plan']['snack2']}")
    print(f"Total Calories: {response['total_calories']}")
    print(f"Notes: {response['notes']}")
    
    return response

def example_conversation_truncation():
    """Example of managing long conversation history."""
    print("\n=== Conversation Truncation Example ===")
    
    # Create a long conversation history
    messages = [
        llm_service.create_message("system", "You are a helpful assistant."),
    ]
    
    # Add many user/assistant exchanges
    for i in range(15):
        messages.append(llm_service.create_message("user", f"This is user message {i+1}"))
        messages.append(llm_service.create_message("assistant", f"This is assistant response {i+1}"))
    
    print(f"Original conversation length: {len(messages)} messages")
    
    # Truncate to manage token limits
    truncated = llm_service.truncate_conversation_history(
        messages=messages,
        max_messages=10,
        preserve_system=True
    )
    
    print(f"Truncated conversation length: {len(truncated)} messages")
    print("Truncated messages:")
    for msg in truncated:
        print(f"  {msg['role']}: {msg['content'][:50]}...")
    
    return truncated

if __name__ == "__main__":
    # Note: These examples won't run without proper API keys and configuration
    # They are provided to demonstrate the usage patterns
    
    print("LLM Service Conversation Examples")
    print("=" * 50)
    print("Note: These examples demonstrate usage patterns.")
    print("Actual execution requires proper OpenAI API configuration.")
    print()
    
    # Show the method signatures and usage patterns
    print("Available conversation methods:")
    print("1. generate_conversation_completion(messages, temperature, max_tokens)")
    print("2. generate_conversation_structured_response(messages, schema, temperature, max_tokens)")
    print("3. create_message(role, content)")
    print("4. build_conversation_messages(system_prompt, conversation_history, user_message)")
    print("5. truncate_conversation_history(messages, max_messages, preserve_system)")
    
    # Uncomment these lines to run examples with proper API configuration:
    # example_basic_conversation()
    # example_conversation_builder()
    # example_structured_conversation()
    # example_conversation_truncation()

"""
LLM Service for handling all API calls to OpenAI and other language models.
Provides a centralized interface for all LLM interactions.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from openai import OpenAI
from config.settings import settings

logger = logging.getLogger(__name__)


class LLMService:
    """Service class for handling all LLM API interactions."""
    
    def __init__(self):
        """Initialize the LLM service with OpenAI client."""
        if not settings.OPENAI_API_KEY:
            raise ValueError("OpenAI API key not found in environment variables")
        
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = settings.OPENAI_MODEL
        self.max_tokens = settings.MAX_TOKENS
        self.temperature = settings.TEMPERATURE
    
    def generate_completion(
        self,
        system_prompt: str,
        user_prompt: str,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """
        Generate a completion without structured output.

        Args:
            system_prompt: The system message to set context
            user_prompt: The user message/prompt
            temperature: Override default temperature
            max_tokens: Override default max tokens

        Returns:
            The generated response text
        """
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            kwargs = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature or self.temperature,
                "max_tokens": max_tokens or self.max_tokens
            }

            response = self.client.chat.completions.create(**kwargs)

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            raise
    
    def generate_structured_response(
        self,
        system_prompt: str,
        user_prompt: str,
        schema: Dict[str, Any],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate a structured response using JSON schema validation.

        Args:
            system_prompt: The system message to set context
            user_prompt: The user message/prompt
            schema: JSON schema for response validation
            temperature: Override default temperature
            max_tokens: Override default max tokens

        Returns:
            The parsed JSON response matching the schema
        """
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            kwargs = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature or self.temperature,
                "max_tokens": max_tokens or self.max_tokens,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "extraction_schema",
                        "strict": True,
                        "schema": schema
                    }
                }
            }

            response = self.client.chat.completions.create(**kwargs)
            content = response.choices[0].message.content.strip()

            # Parse and return the structured response
            return json.loads(content)

        except Exception as e:
            logger.error(f"Error generating structured response: {e}")
            # Fallback to regular completion with JSON instruction in prompt
            logger.info("Falling back to regular completion with JSON instruction")
            fallback_system_prompt = f"{system_prompt}\n\nIMPORTANT: Respond with valid JSON only."
            fallback_response = self.generate_completion(
                system_prompt=fallback_system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return self.parse_json_response(fallback_response)

    def parse_json_response(self, response: str) -> Dict[str, Any]:
        """
        Parse a JSON response from the LLM.

        Args:
            response: The raw response string

        Returns:
            Parsed JSON as a dictionary
        """
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            logger.error(f"Response content: {response}")
            raise ValueError(f"Invalid JSON response: {e}")

    def generate_conversation_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """
        Generate a completion for a multi-message conversation.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys.
                     Role can be 'system', 'user', or 'assistant'
            temperature: Override default temperature
            max_tokens: Override default max tokens

        Returns:
            The generated response text
        """
        try:
            # Validate message format
            for msg in messages:
                if not isinstance(msg, dict) or 'role' not in msg or 'content' not in msg:
                    raise ValueError("Each message must be a dict with 'role' and 'content' keys")
                if msg['role'] not in ['system', 'user', 'assistant']:
                    raise ValueError("Message role must be 'system', 'user', or 'assistant'")

            kwargs = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature or self.temperature,
                "max_tokens": max_tokens or self.max_tokens
            }

            response = self.client.chat.completions.create(**kwargs)

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error generating conversation completion: {e}")
            raise

    def generate_conversation_structured_response(
        self,
        messages: List[Dict[str, str]],
        schema: Dict[str, Any],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate a structured response for a multi-message conversation using JSON schema validation.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys.
                     Role can be 'system', 'user', or 'assistant'
            schema: JSON schema for response validation
            temperature: Override default temperature
            max_tokens: Override default max tokens

        Returns:
            The parsed JSON response matching the schema
        """
        try:
            # Validate message format
            for msg in messages:
                if not isinstance(msg, dict) or 'role' not in msg or 'content' not in msg:
                    raise ValueError("Each message must be a dict with 'role' and 'content' keys")
                if msg['role'] not in ['system', 'user', 'assistant']:
                    raise ValueError("Message role must be 'system', 'user', or 'assistant'")

            kwargs = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature or self.temperature,
                "max_tokens": max_tokens or self.max_tokens,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "extraction_schema",
                        "strict": True,
                        "schema": schema
                    }
                }
            }

            response = self.client.chat.completions.create(**kwargs)
            content = response.choices[0].message.content.strip()

            # Parse and return the structured response
            return json.loads(content)

        except Exception as e:
            logger.error(f"Error generating conversation structured response: {e}")
            # Fallback to regular conversation completion with JSON instruction
            logger.info("Falling back to regular conversation completion with JSON instruction")

            # Add JSON instruction to the last message or create a new system message
            fallback_messages = messages.copy()
            if fallback_messages and fallback_messages[-1]['role'] == 'system':
                fallback_messages[-1]['content'] += "\n\nIMPORTANT: Respond with valid JSON only."
            else:
                fallback_messages.append({
                    "role": "system",
                    "content": "IMPORTANT: Respond with valid JSON only."
                })

            fallback_response = self.generate_conversation_completion(
                messages=fallback_messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return self.parse_json_response(fallback_response)

    def create_message(self, role: str, content: str) -> Dict[str, str]:
        """
        Create a properly formatted message for conversation.

        Args:
            role: The role of the message ('system', 'user', or 'assistant')
            content: The content of the message

        Returns:
            Formatted message dictionary
        """
        if role not in ['system', 'user', 'assistant']:
            raise ValueError("Role must be 'system', 'user', or 'assistant'")

        return {"role": role, "content": content}

    def build_conversation_messages(
        self,
        system_prompt: Optional[str] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        user_message: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """
        Build a complete conversation message list from components.

        Args:
            system_prompt: Optional system prompt to start the conversation
            conversation_history: Optional list of previous messages
            user_message: Optional new user message to append

        Returns:
            Complete list of conversation messages
        """
        messages = []

        # Add system prompt if provided
        if system_prompt:
            messages.append(self.create_message("system", system_prompt))

        # Add conversation history if provided
        if conversation_history:
            # Validate and add history messages
            for msg in conversation_history:
                if not isinstance(msg, dict) or 'role' not in msg or 'content' not in msg:
                    raise ValueError("Each history message must be a dict with 'role' and 'content' keys")
                messages.append(msg)

        # Add new user message if provided
        if user_message:
            messages.append(self.create_message("user", user_message))

        return messages

    def truncate_conversation_history(
        self,
        messages: List[Dict[str, str]],
        max_messages: int = 20,
        preserve_system: bool = True
    ) -> List[Dict[str, str]]:
        """
        Truncate conversation history to manage token limits while preserving important context.

        Args:
            messages: List of conversation messages
            max_messages: Maximum number of messages to keep
            preserve_system: Whether to always preserve system messages

        Returns:
            Truncated list of messages
        """
        if len(messages) <= max_messages:
            return messages

        truncated = []
        system_messages = []
        other_messages = []

        # Separate system messages from others
        for msg in messages:
            if msg['role'] == 'system' and preserve_system:
                system_messages.append(msg)
            else:
                other_messages.append(msg)

        # Always include system messages if preserving them
        if preserve_system:
            truncated.extend(system_messages)
            remaining_slots = max_messages - len(system_messages)
        else:
            remaining_slots = max_messages

        # Take the most recent messages up to the limit
        if remaining_slots > 0 and other_messages:
            truncated.extend(other_messages[-remaining_slots:])

        return truncated


# Global LLM service instance
llm_service = LLMService()
